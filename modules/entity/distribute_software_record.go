package entity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DistributeSoftwareRecord struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProductId   string             `json:"product_id" bson:"product_id"`     // 产品id
	BatchId     string             `json:"batch_id" bson:"batch_id"`         // 批次id
	TaskId      string             `json:"task_id" bson:"task_id"`           // 任务id
	Rid         string             `json:"rid" bson:"rid"`                   // 计划id
	SoftId      string             `json:"soft_id" bson:"soft_id"`           // 软件id
	Name        string             `json:"name" bson:"name"`                 // 软件名称
	Logo        string             `json:"logo" bson:"logo"`                 // 软件logo
	ClientId    string             `json:"client_id" bson:"client_id"`       // 客户端id
	UserInfo    *UserInfo          `json:"user_info" bson:"user_info"`       // 用户信息
	HostInfo    *SoftwareHostInfo  `json:"host_info" bson:"host_info"`       // 主机信息
	DeviceGroup []*DeviceGroup     `json:"device_group" bson:"device_group"` // 设备组
	Status      int                `json:"status" bson:"status"`             // 状态
	Reason      string             `json:"reason" bson:"reason"`             // 原因
	ExecTime    int64              `json:"exec_time" bson:"exec_time"`       // 执行时间
	CreatedAt   int64              `json:"created_at" bson:"created_at"`     // 创建时间
	UpdatedAt   int64              `json:"updated_at" bson:"updated_at"`     // 更新时间
	ExpiredAt   time.Time          `json:"expired_at" bson:"expired_at"`     // 过期时间
	UserInfos   []*UserInfo        `json:"user_infos" bson:"user_infos"`     // 用户信息
	UserID      string             `json:"user_id" bson:"user_id"`           // 用户id
}

type SoftwareHostInfo struct {
	RegisterName string `json:"register_name" bson:"register_name"`
	HostName     string `json:"host_name" bson:"host_name"`
	Platform     int    `json:"platform" bson:"platform"`
	WinVersion   string `json:"win_version" bson:"win_version"`
}

func (b *DistributeSoftwareRecord) GetCreatedAt() int64 {
	return b.CreatedAt
}

func (b *DistributeSoftwareRecord) SetCreatedAt(i int64) {

	b.CreatedAt = i
}

func (b *DistributeSoftwareRecord) GetUpdatedAt() int64 {
	return b.CreatedAt // 在这个案例中，我们使用CreatedAt作为UpdatedAt
}

func (b *DistributeSoftwareRecord) SetUpdatedAt(i int64) {
	b.CreatedAt = i
}

func (b *DistributeSoftwareRecord) GetExpireAt() time.Time {
	return time.Now()
}

func (b *DistributeSoftwareRecord) SetExpireAt(time2 time.Time) {

}

func (b *DistributeSoftwareRecord) SetLogCode(logCode string) {
	b.ID = primitive.NewObjectID()
}
func (b *DistributeSoftwareRecord) GetLogCode() string {
	return b.ID.Hex()
}
