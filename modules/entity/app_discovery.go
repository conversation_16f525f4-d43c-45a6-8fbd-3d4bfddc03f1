package entity

import "time"

type AppDiscovery struct {
	StrategyId string `json:"strategy_id" bson:"strategy_id"`

	AppName     string `json:"app_name" bson:"app_name"`
	AppAddress  string `json:"app_address" bson:"app_address"`
	AppDomain   string `json:"app_domain" bson:"app_domain"`
	AppPort     string `json:"app_port" bson:"app_port"`
	AppVisitNum int    `json:"app_visit_num" bson:"app_visit_num"`

	ClientID     string `json:"client_id" bson:"client_id"`
	OrgName      string `json:"org_name" bson:"org_name"`
	RegisterName string `json:"register_name" bson:"register_name"`
	Operator     string `json:"operator" bson:"operator"`

	CreatedAt   int64     `json:"created_at" bson:"created_at"`
	UpdatedAt   int64     `json:"updated_at" bson:"updated_at"`
	ExpiredAt   time.Time `json:"expired_at" bson:"expired_at"`
	LogCode     string    `json:"log_code" bson:"log_code"`
	TriggerTime int64     `json:"trigger_time" bson:"trigger_time"`

	State     int    `json:"state" bson:"state"` //0:不处理 1:忽略
	SubDomain string `json:"sub_domain" bson:"sub_domain"`
}

func (b *AppDiscovery) GetCreatedAt() int64 {
	return b.CreatedAt
}

func (b *AppDiscovery) SetCreatedAt(i int64) {

	b.CreatedAt = i
}

func (b *AppDiscovery) GetUpdatedAt() int64 {
	return b.UpdatedAt // 在这个案例中，我们使用CreatedAt作为UpdatedAt
}

func (b *AppDiscovery) SetUpdatedAt(i int64) {
	b.UpdatedAt = i
}

func (b *AppDiscovery) GetExpireAt() time.Time {
	return b.ExpiredAt
}

func (b *AppDiscovery) SetExpireAt(time2 time.Time) {
	b.ExpiredAt = time2
}

func (b *AppDiscovery) SetLogCode(logCode string) {
	b.LogCode = logCode
}

func (b *AppDiscovery) GetLogCode() string {
	return b.LogCode
}
