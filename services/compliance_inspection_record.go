package services

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/go-basic/uuid"
	"github.com/spf13/cast"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"

	"go.mongodb.org/mongo-driver/mongo"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"
)

const (
	ComplianceInspectionRecordCollection = "compliance_inspection_record"
)

// ComplianceInspectionRecordRepository 漏洞主机关系服务
type ComplianceInspectionRecordRepository struct {
	Collection *mongo.Collection
}

func NewComplianceInspectionRecordRepository() *ComplianceInspectionRecordRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(ComplianceInspectionRecordCollection)
	}
	return &ComplianceInspectionRecordRepository{
		Collection: collection,
	}
}

func (s *ComplianceInspectionRecordRepository) BatchInsert(ctx context.Context, records []entity.ComplianceInspectionRecord) error {
	// 空数组直接返回
	if len(records) == 0 {
		return nil
	}

	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}

		record.ExpiredAt = time.Now().AddDate(0, 0, conf.Service.DataRetentionDays)

		documents = append(documents, record)
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)
	return err
}

func (s *ComplianceInspectionRecordRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	data := &dto.ComplianceInspectionReport{}

	if err := json.Unmarshal([]byte(msg.Data), data); err != nil {
		log.Errorf("error from service [DealTask] dealComplianceInspection json.Unmarshal: %v", err)
		return err
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetHostInfo: %v", err)
	}

	var hostName, macAddress, registerName string
	var platform int
	var hostInfo *entity.HostInfo
	groupInfo := make([]*entity.GroupInfo, 0)

	if len(hostData) > 0 {
		hostName = hostData[0].Hostname
		platform = hostData[0].Platform
		macAddress = hostData[0].MacAddress
		registerName = hostData[0].RegisterName

		hostInfo = &entity.HostInfo{
			HostName:     hostData[0].Hostname,
			Platform:     hostData[0].Platform,
			WinVersion:   hostData[0].WinVersion,
			MacAddress:   hostData[0].MacAddress,
			OrgConnectIp: hostData[0].Orgconnectip,
			Remarks:      hostData[0].Remarks,
			InstallTime:  hostData[0].InstallTime,
		}

		for _, group := range hostData[0].GroupInfos {
			groupInfo = append(groupInfo, &entity.GroupInfo{
				GID:   cast.ToInt64(group.Gid),
				GName: group.GroupName,
			})
		}
	}

	var userCode, userName, userEmail string
	var userInfos []*entity.UserInfo
	var userInfo *entity.UserInfo
	if msg.UserCode != "" {
		userData, err := AttributeDataService.GetUserInfo(msg.UserCode, msg.OrgName)

		if err != nil {
			log.Errorf("error from service [DealTask] AttributeDataService.GetUserInfo: %v", err)
		}

		userInfos = entity.ConvertUserInfoResponse2UserInfo(userData)

		if len(userData) > 0 {
			userName = userData[0].UserName
			userEmail = userData[0].Email
			userInfo = &entity.UserInfo{
				UserName:  userData[0].UserName,
				UserCode:  userData[0].UserCode,
				UserGroup: userData[0].UserGroup,
				DeptPath:  userData[0].DeptPath,
			}
		}
	}

	// 批量插入详情
	complianceInspectionRecordData := make([]entity.ComplianceInspectionRecord, 0)
	complianceDetails := make([]*entity.ComplianceDetails, 0)
	complianceDetailsMapForDynamicControl := make(map[string]bool, 0)

	var notComplianceNum int
	for _, v := range data.ComplianceDetails {

		complianceDetailsMapForDynamicControl[v.Key] = v.IsCompliant

		if v.IsCompliant == false {
			notComplianceNum++
		}

		complianceDetails = append(complianceDetails, &entity.ComplianceDetails{
			Key:             v.Key,
			IsCompliant:     v.IsCompliant,
			Msg:             v.Msg,
			Reason:          v.Reason,
			ComplianceCheck: v.ComplianceCheck,
		})
	}

	complianceInspectionRecordData = append(complianceInspectionRecordData, entity.ComplianceInspectionRecord{
		ProductId:         msg.OrgName,
		ClientId:          msg.ClientId,
		HostInfo:          hostInfo,
		HostName:          hostName,
		RegisterName:      registerName,
		MacAddress:        macAddress,
		Platform:          platform,
		BatchId:           msg.BatchId,
		StrategyId:        data.StrategyId,
		StrategyName:      data.StrategyName,
		Status:            data.Status,
		Disposal:          data.Disposal,
		UserInfo:          userInfo,
		TriggerTime:       data.TriggerTime,
		ComplianceDetails: complianceDetails,
		UserInfos:         userInfos,
		UserID:            msg.UserCode,
		Token:             msg.Token,
	})

	complianceInspection := make([]entity.ComplianceInspection, 0)
	complianceInspection = append(complianceInspection, entity.ComplianceInspection{
		ProductId:        msg.OrgName,
		ClientId:         msg.ClientId,
		HostInfo:         hostInfo,
		StrategyId:       data.StrategyId,
		Status:           data.Status,
		Disposal:         data.Disposal,
		NotComplianceNum: notComplianceNum,
		TriggerTime:      data.TriggerTime,
		UserCode:         userCode,
		UserInfo:         userInfo,
		DeviceGroup:      groupInfo,
		UserInfos:        userInfos,
		UserID:           msg.UserCode,
	})

	complianceInspectionClientData := make([]entity.ComplianceInspectionClient, 0)
	complianceInspectionClientData = append(complianceInspectionClientData, entity.ComplianceInspectionClient{
		ProductId:        msg.OrgName,
		ClientId:         msg.ClientId,
		HostInfo:         hostInfo,
		Status:           data.Status,
		NotComplianceNum: notComplianceNum,
		TriggerTime:      data.TriggerTime,
		UserCode:         userCode,
		UserInfo:         userInfo,
		DeviceGroup:      groupInfo,
		UserInfos:        userInfos,
		UserID:           msg.UserCode,
	})

	if err = ComplianceInspectionService.BatchUpsertComplianceInspection(context.Background(), complianceInspection); err != nil {
		log.Errorf("error from service [DealTask] ComplianceInspectionService.BatchUpsertComplianceInspection: %v", err)
	}

	if err = ComplianceInspectionRecordService.BatchInsert(context.Background(), complianceInspectionRecordData); err != nil {
		log.Errorf("error from service [DealTask] ComplianceInspectionRecordService.BatchInsert: %v", err)
	}

	if err = ComplianceInspectionClientService.BatchInsert(context.Background(), complianceInspectionClientData); err != nil {
		log.Errorf("error from service [DealTask] ComplianceInspectionClientService.BatchInsert: %v", err)
	}

	var listIssueTitle []string
	if data.Disposal == 3 {
		for _, v := range data.ComplianceDetails {
			// 发送通知，且不合规，发送通知
			if info, ok := dto.ComplianceCheckKeyNotify[v.Key]; ok && !v.IsCompliant {
				listIssueTitle = append(listIssueTitle, info)
			}
		}
	}

	// 发送邮件通知
	if len(listIssueTitle) > 0 {
		go func(listIssueTitle []string) {
			var receiveDirectNotify []dto.ReceiveDirectNotify
			receiveDirectNotify = append(receiveDirectNotify, dto.ReceiveDirectNotify{
				NotifyTarget: "user",
				TplType:      "compliance",
				ProductID:    msg.OrgName,
				UserCode:     msg.UserCode,
				Params: dto.Params{
					UserName:       userName,
					UserEmail:      userEmail,
					DeviceName:     hostName,
					Timestamp:      time.Unix(data.TriggerTime, 0).Format(time.DateTime),
					ListIssueTitle: strings.Join(listIssueTitle, ","),
				},
			})

			dataJson, err := json.Marshal(receiveDirectNotify)

			if err != nil {
				log.Errorf("error from service [DealTask] json.Marshal: %v", err)
			}

			url := conf.Service.ReceiveDirectNotifyAddr + "/sase_notify/api/v1/receive_direct_notify"

			if _, err = DoRequest("POST", url, nil, dataJson); err != nil {
				log.Errorf("error from service [DealTask] SendReceiveDirectNotify: %v", err)
			}
		}(listIssueTitle)
	}

	if len(complianceDetailsMapForDynamicControl) > 0 {
		go func(complianceDetailsMapForDynamicControl map[string]bool) {
			dynamicControlReportData := dto.DynamicControlReportData{
				UserCode:    msg.UserCode,
				ProductID:   msg.OrgName,
				ClientId:    msg.ClientId,
				CheckId:     data.StrategyId + "_" + data.BatchId,
				CheckResult: complianceDetailsMapForDynamicControl,
				Token:       msg.Token,
			}

			dynamicControlReportDataJson, _ := json.Marshal(dynamicControlReportData)
			// 上报动态管控数据
			SendDynamicControlMessage([]dto.DynamicControlReportRequest{
				{
					Action: "device_compliance",
					ReqId:  uuid.New(),
					Time:   cast.ToString(time.Now().Unix()),
					Data:   string(dynamicControlReportDataJson),
				},
			})
		}(complianceDetailsMapForDynamicControl)
	}
	return nil
}
