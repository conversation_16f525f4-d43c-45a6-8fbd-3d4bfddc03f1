package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"

	"github.com/go-basic/uuid"
	"github.com/jpillora/go-tld"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	LogOfAppDiscovery = "logs_app_discovery"
)

// AppDiscoveryRepository  应用发现
type AppDiscoveryRepository struct {
	Collection *mongo.Collection
	ExpiredDay time.Duration
}

func NewAppDiscoveryRepository() IHandlerService[entity.AppDiscovery] {
	expiredDay := time.Hour * 24 * time.Duration(conf.Service.BehaviorInterceptExpiredDay)
	if expiredDay == 0 {
		expiredDay = time.Hour * 24 * 10 // 默认10天
	}
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(LogOfAppDiscovery)
	}
	return &AppDiscoveryRepository{
		Collection: collection,
		ExpiredDay: expiredDay,
	}
}
func (a *AppDiscoveryRepository) BatchInsert(ctx context.Context, records []entity.AppDiscovery) error {

	if len(records) == 0 {
		return nil
	}

	writeModels := make([]mongo.WriteModel, 0, len(records))
	for _, doc := range records {
		filter := bson.M{
			"state":      1,
			"app_domain": doc.AppDomain,
		}

		update := bson.M{
			"$set": bson.M{
				"updated_at": time.Now().Unix(), // 始终更新 updated_at
			},
			"$setOnInsert": bson.M{
				"strategy_id":   doc.StrategyId,
				"app_name":      doc.AppName,
				"app_address":   doc.AppAddress,
				"app_domain":    doc.AppDomain,
				"app_port":      doc.AppPort,
				"app_visit_num": doc.AppVisitNum,
				"client_id":     doc.ClientID,
				"org_name":      doc.OrgName,
				"register_name": doc.RegisterName,
				"operator":      doc.Operator,
				"created_at":    time.Now().Unix(),
				"sub_domain":    doc.SubDomain,
				"expired_at":    doc.ExpiredAt,
				"log_code":      doc.LogCode,
				"trigger_time":  doc.TriggerTime,
				"state":         0, // 新插入的文档 state=0
			},
		}
		writeModels = append(writeModels, mongo.NewUpdateOneModel().SetFilter(filter).SetUpdate(update).SetUpsert(true))

	}

	_, err := a.Collection.BulkWrite(ctx, writeModels)
	if err != nil {
		log.Errorf("failed to make insert to collection error %s", err)
	}
	return err

}

func (a *AppDiscoveryRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	// 解析msg.Data
	fireWalls := make([]dto.AppDiscovery, 0)
	err := json.Unmarshal([]byte(msg.Data), &fireWalls)
	if err != nil {
		return err
	}
	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)

	if len(hostData) == 0 {
		hostData = []dto.HostInfoResponse{
			{
				Hostname: "unknown",
			},
		}
	}

	hostInfo := hostData[0]

	data := make([]entity.AppDiscovery, 0, len(fireWalls))
	for _, item := range fireWalls {
		data = append(data, entity.AppDiscovery{
			StrategyId:   item.StrategyID,
			AppName:      item.AppName,
			AppAddress:   item.AppAddress,
			AppDomain:    item.AppDomain,
			AppPort:      item.AppPort,
			AppVisitNum:  item.AppVisitNum,
			ClientID:     msg.ClientId,
			OrgName:      msg.OrgName,
			Operator:     hostInfo.Username,
			TriggerTime:  item.TriggerTime,
			SubDomain:    a.getSubDomain(item.AppDomain),
			CreatedAt:    time.Now().Unix(),
			UpdatedAt:    time.Now().Unix(),
			ExpiredAt:    time.Now().Add(a.ExpiredDay),
			LogCode:      uuid.New(),
			RegisterName: hostInfo.RegisterName,
		})
	}
	err = a.BatchInsert(ctx, data)
	if err != nil {
		log.Errorf("error from service [DealTask] AppDiscoveryRepository.BatchInsert: %v", err)
	}
	return nil

}

func (a *AppDiscoveryRepository) getSubDomain(domain string) string {
	parsed, err := tld.Parse("https://" + domain)
	if err != nil {
		log.Errorf("failed to parse domain error %s", err)
		return domain
	}
	log.Infof("get subdomain from %+v", parsed)

	return fmt.Sprintf("*.%s.%s", parsed.Domain, parsed.TLD)
}
