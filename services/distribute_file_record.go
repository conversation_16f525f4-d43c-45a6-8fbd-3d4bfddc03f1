package services

import (
	"context"
	"encoding/json"
	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"time"

	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"

	"go.mongodb.org/mongo-driver/mongo"
)

const (
	DistributeFileRecordTableCollection = "distribute_file_record"
)

// DistributeFileRecordRepository 分发软件记录服务
type DistributeFileRecordRepository struct {
	Collection *mongo.Collection
}

// NewDistributeFileRecordRepository 创建分发软件记录服务实例
func NewDistributeFileRecordRepository() *DistributeFileRecordRepository {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(DistributeFileRecordTableCollection)
	}
	return &DistributeFileRecordRepository{
		Collection: collection,
	}
}

// BatchInsert 批量添加文件分发记录
func (s *DistributeFileRecordRepository) BatchInsert(ctx context.Context, records []entity.DistributeFileRecord) error {
	// 空数组直接返回
	if len(records) == 0 {
		return nil
	}

	// 准备要插入的文档
	var documents []interface{}
	now := time.Now().Unix()

	// 设置创建和更新时间
	for _, record := range records {
		if record.CreatedAt == 0 {
			record.CreatedAt = now
		}
		if record.UpdatedAt == 0 {
			record.UpdatedAt = now
		}
		record.ExpiredAt = time.Now().AddDate(0, 0, conf.Service.DataRetentionDays)

		documents = append(documents, record)
	}

	// 执行批量插入操作
	_, err := s.Collection.InsertMany(ctx, documents)
	return err
}

func (s *DistributeFileRecordRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	distributeFile := make([]dto.DistributeFileReport, 0)
	err := json.Unmarshal([]byte(msg.Data), &distributeFile)
	if err != nil {
		return err
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] dealDistributeFile.AttributeDataService.GetHostInfo: %v", err)
	}

	var hostInfo *entity.HostInfo
	var userInfo *entity.UserInfo

	if len(hostData) > 0 {
		hostInfo = &entity.HostInfo{
			HostName:     hostData[0].Hostname,
			Platform:     hostData[0].Platform,
			WinVersion:   hostData[0].WinVersion,
			MacAddress:   hostData[0].MacAddress,
			OrgConnectIp: hostData[0].Orgconnectip,
			Remarks:      hostData[0].Remarks,
			InstallTime:  hostData[0].InstallTime,
			RegisterName: hostData[0].RegisterName,
		}
	}

	userData, err := AttributeDataService.GetUserInfo(msg.UserCode, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] AttributeDataService.GetUserInfo: %v", err)
	}

	if len(userData) > 0 {
		userInfo = &entity.UserInfo{
			UserName:  userData[0].UserName,
			UserCode:  userData[0].UserCode,
			UserGroup: userData[0].UserGroup,
			DeptPath:  userData[0].DeptPath,
		}
	}

	userInfos := entity.ConvertUserInfoResponse2UserInfo(userData)
	distributeFileInsertData := make([]entity.DistributeFileRecord, 0)

	for _, item := range distributeFile {
		distributeFileInsertData = append(distributeFileInsertData, entity.DistributeFileRecord{
			ClientId:   msg.ClientId,
			ProductId:  msg.OrgName,
			TaskId:     item.TaskId,
			BatchId:    msg.BatchId,
			Rid:        item.Rid,
			ExecTime:   item.ExecTime,
			Status:     item.Status,
			Remarks:    item.Remarks,
			FileCode:   item.FileCode,
			FileName:   item.FileName,
			FileResult: item.FileResult,
			UserInfo:   userInfo,
			HostInfo:   hostInfo,
			UserInfos:  userInfos,
			UserID:     msg.UserCode,
		})
	}

	if err = s.BatchInsert(context.Background(), distributeFileInsertData); err != nil {
		log.Errorf("error from service [DealTask] DistributeFileRecordService.BatchInsert: %v", err)
	}

	return nil
}
