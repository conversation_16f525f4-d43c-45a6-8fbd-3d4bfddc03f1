package services

import (
	"context"
	"encoding/json"
	"time"

	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/mongo"

	"sase-strategy-report/common/dto"
	"sase-strategy-report/library/log"
	"sase-strategy-report/modules/client"
	"sase-strategy-report/modules/entity"
)

const (
	DistributeSoftwareTableCollection = "distribute_software_record"
)

// DistributeSoftwareRecordRepository 分发软件记录服务
type DistributeSoftwareRecordRepository struct {
	Collection *mongo.Collection
}

// NewDistributeSoftwareRecordRepository 创建分发软件记录服务实例
func NewDistributeSoftwareRecordRepository() IHandlerService[entity.DistributeSoftwareRecord] {
	var collection *mongo.Collection
	if client.MongodbSaseReport != nil {
		collection = client.MongodbSaseReport.Database.Collection(DistributeSoftwareTableCollection)
	}
	return &DistributeSoftwareRecordRepository{
		Collection: collection,
	}
}

// BatchInsert 批量添加软件分发记录
func (s *DistributeSoftwareRecordRepository) BatchInsert(ctx context.Context, records []entity.DistributeSoftwareRecord) error {
	// 空数组直接返回
	if len(records) == 0 {
		return nil
	}

	// 将records转换为TimestampEntity接口类型
	interfaceRecords := make([]entity.TimestampEntity, len(records))
	for i, v := range records {
		interfaceRecords[i] = &v
	}

	return entity.GenericBatchInsert(ctx, s.Collection, interfaceRecords, time.Now())
}

func (s *DistributeSoftwareRecordRepository) Handle(ctx context.Context, msg *dto.KafkaReadStruct) error {
	distributeSoftware := make([]dto.DistributeSoftware, 0)
	err := json.Unmarshal([]byte(msg.Data), &distributeSoftware)
	if err != nil {
		return err
	}

	hostData, err := AttributeDataService.GetHostInfo(msg.ClientId, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] dealDistributeSoftware.AttributeDataService.GetHostInfo: %v", err)
	}

	var hostName string
	var registerName string
	var platform int
	var winVersion string
	groupInfo := make([]*entity.DeviceGroup, 0)

	if len(hostData) > 0 {
		hostName = hostData[0].Hostname
		platform = hostData[0].Platform
		winVersion = hostData[0].WinVersion
		registerName = hostData[0].RegisterName
		for _, group := range hostData[0].GroupInfos {
			groupInfo = append(groupInfo, &entity.DeviceGroup{
				GID:   cast.ToInt64(group.Gid),
				GName: group.GroupName,
			})
		}
	}

	userData, err := AttributeDataService.GetUserInfo(msg.UserCode, msg.OrgName)
	if err != nil {
		log.Errorf("error from service [DealTask] DistributeSoftwareRecordRepository.AttributeDataService.GetUserInfo: %v", err)
	}

	var userInfo entity.UserInfo
	if len(userData) > 0 {
		userInfo = entity.UserInfo{
			UserName:  userData[0].UserName,
			UserCode:  userData[0].UserCode,
			UserGroup: userData[0].UserGroup,
			DeptPath:  userData[0].DeptPath,
		}
	}

	userInfos := entity.ConvertUserInfoResponse2UserInfo(userData)

	distributeSoftwareInsertData := make([]entity.DistributeSoftwareRecord, 0)

	for _, item := range distributeSoftware {
		distributeSoftwareInsertData = append(distributeSoftwareInsertData, entity.DistributeSoftwareRecord{
			ClientId:  msg.ClientId,
			ProductId: msg.OrgName,
			TaskId:    item.TaskId,
			BatchId:   msg.BatchId,
			Rid:       item.Rid,
			SoftId:    item.SoftId,
			Name:      item.Name,
			Logo:      item.Logo,
			ExecTime:  item.ExecTime,
			Status:    item.Status,
			Reason:    item.Reason,
			UserInfo:  &userInfo,
			HostInfo: &entity.SoftwareHostInfo{
				HostName:     hostName,
				Platform:     platform,
				WinVersion:   winVersion,
				RegisterName: registerName,
			},
			DeviceGroup: groupInfo,
			UserInfos:   userInfos,
			UserID:      msg.UserCode,
			CreatedAt:   time.Now().Unix(),
			UpdatedAt:   time.Now().Unix(),
			ExpiredAt:   time.Now().AddDate(0, 0, conf.Service.DataRetentionDays),
		})
	}

	if err = s.BatchInsert(context.Background(), distributeSoftwareInsertData); err != nil {
		log.Errorf("error from service [DealTask] DistributeSoftwareRecordService.BatchInsert: %v", err)
	}

	return nil
}
